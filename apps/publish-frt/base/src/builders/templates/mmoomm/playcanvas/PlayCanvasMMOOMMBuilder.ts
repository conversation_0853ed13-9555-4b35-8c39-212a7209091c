// Universo Platformo | PlayCanvas MMOOMM Builder
// Advanced MMO template builder with modular architecture

import { AbstractTemplateBuilder } from '../../../common/AbstractTemplateBuilder'
import { BuildOptions, TemplateConfig } from '../../../common/types'
import type { IFlowData, IUPDLMultiScene } from '@universo/publish-srv'
import { MMOOMMTemplateConfig } from './config'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ComponentHandler, EventHandler, ActionHandler, DataHandler, UniversoHandler } from './handlers'
import { getDefaultRotatorScript, setupScriptSystem } from './scripts'
import { createMMOOMMBuilderSystemsManager, IBuilderSystemsManager } from './handlers/shared/builderSystems'

export class PlayCanvasMMOOMMBuilder extends AbstractTemplateBuilder {
    private spaceHandler = new SpaceHandler()
    private entityHandler = new EntityHandler()
    private componentHandler = new ComponentHandler()
    private eventHandler = new EventHandler()
    private actionHandler = new ActionHandler()
    private dataHandler = new DataHandler()
    private universoHandler = new UniversoHandler()
    private builderSystemsManager: IBuilderSystemsManager

    constructor() {
        super('mmoomm')

        // Initialize script system for MMOOMM template
        setupScriptSystem()

        // Initialize modular builder systems
        this.builderSystemsManager = createMMOOMMBuilderSystemsManager()
        console.log('[PlayCanvasMMOOMMBuilder] Modular systems initialized')
    }

    /**
     * Build PlayCanvas MMOOMM HTML from flow data
     */
    async build(flowData: IFlowData, options: BuildOptions = {}): Promise<string> {
        console.log('[PlayCanvasMMOOMMBuilder] Building MMOOMM project with modular systems')

        try {
            // Check for multiplayer mode first
            const multiplayerInfo = this.detectMultiplayerMode(flowData)

            if (multiplayerInfo.isMultiplayer) {
                console.log('[PlayCanvasMMOOMMBuilder] Multiplayer mode detected, building multiplayer scene')
                return this.buildMultiplayerScene(multiplayerInfo, options)
            }

            // Single scene processing
            if (flowData.updlSpace && !flowData.multiScene) {
                console.log('[PlayCanvasMMOOMMBuilder] Building single scene MMOOMM')
                const nodes = this.extractMMOOMMNodes(flowData)

                // Check if we have only Space node with no other content
                const hasContent =
                    nodes.entities.length > 0 ||
                    nodes.components.length > 0 ||
                    nodes.events.length > 0 ||
                    nodes.actions.length > 0 ||
                    nodes.data.length > 0 ||
                    nodes.universo.length > 0

                if (!hasContent) {
                    console.log('[PlayCanvasMMOOMMBuilder] Only Space node found, generating default scene')
                    return this.generateDefaultScene(options)
                }

                return this.buildSingleScene(flowData, options)
            }

            // Multi-scene processing
            if (flowData.multiScene) {
                console.log('[PlayCanvasMMOOMMBuilder] Building multi-scene MMOOMM:', {
                    totalScenes: flowData.multiScene.totalScenes
                })
                return this.buildMultiScene(flowData.multiScene, options)
            }

            // Fallback - generate default scene
            console.warn('[PlayCanvasMMOOMMBuilder] No updlSpace or multiScene, generating default scene')
            return this.generateDefaultScene(options)
        } catch (error) {
            console.error('[PlayCanvasMMOOMMBuilder] Build error:', error)
            return this.generateDefaultScene(options)
        }
    }

    /**
     * Build single scene MMOOMM using modular systems
     */
    private buildSingleScene(flowData: IFlowData, options: BuildOptions): string {
        const nodes = this.extractMMOOMMNodes(flowData)

        console.log(`[PlayCanvasMMOOMMBuilder] Processing nodes - Entities: ${nodes.entities.length}, Components: ${nodes.components.length}`)

        // Process all node types using handlers
        const spaceScript = this.spaceHandler.process(nodes.spaces[0], options)
        const entityScript = this.entityHandler.process(nodes.entities, options)
        const componentScript = this.componentHandler.process(nodes.components, options)
        const eventScript = this.eventHandler.process(nodes.events, options)
        const actionScript = this.actionHandler.process(nodes.actions, options)
        const dataScript = this.dataHandler.process(nodes.data, options)
        const universoScript = this.universoHandler.process(nodes.universo, options)

        // Combine all scripts into executable code
        const combinedScript = [
            '// PlayCanvas MMOOMM Scene - Generated by Universo Platformo',
            '// Multi-user virtual world with real-time synchronization',
            '',
            '// MMO Space setup',
            spaceScript,
            '',
            '// Entities with MMO capabilities',
            entityScript,
            '',
            '// Components',
            componentScript,
            '',
            '// Real-time Events',
            eventScript,
            '',
            '// Network Actions',
            actionScript,
            '',
            '// Data synchronization',
            dataScript,
            '',
            '// Universo networking gateway',
            universoScript,
            '',
            '// Start PlayCanvas application',
            'app.start();'
        ].join('\n')

        // Use modular systems manager to generate complete HTML
        return this.builderSystemsManager.generateCompleteHTML(combinedScript, options)
    }

    /**
     * Build multi-scene MMOOMM using modular systems
     */
    private buildMultiScene(multiScene: IUPDLMultiScene, options: BuildOptions): string {
        // For multi-scene, we process each scene and combine them
        const scenes = multiScene.scenes || []
        const allEntities: any[] = []
        const allComponents: any[] = []
        const allEvents: any[] = []
        const allActions: any[] = []
        const allData: any[] = []
        const allUniverso: any[] = []

        // Collect nodes from all scenes
        scenes.forEach((scene) => {
            if (scene.spaceData) {
                allEntities.push(...(scene.spaceData.entities || []))
                allComponents.push(...(scene.spaceData.components || []))
                allEvents.push(...(scene.spaceData.events || []))
                allActions.push(...(scene.spaceData.actions || []))
                allData.push(...(scene.spaceData.data || []))
                allUniverso.push(...(scene.spaceData.universo || []))
            }
        })

        console.log(`[PlayCanvasMMOOMMBuilder] Multi-scene processing - Total scenes: ${scenes.length}, Entities: ${allEntities.length}, Components: ${allComponents.length}`)

        // Process using handlers
        const spaceScript = this.spaceHandler.process({ data: { type: 'root', id: 'multi-scene' } }, options)
        const entityScript = this.entityHandler.process(allEntities, options)
        const componentScript = this.componentHandler.process(allComponents, options)
        const eventScript = this.eventHandler.process(allEvents, options)
        const actionScript = this.actionHandler.process(allActions, options)
        const dataScript = this.dataHandler.process(allData, options)
        const universoScript = this.universoHandler.process(allUniverso, options)

        // Combine multi-scene script
        const combinedScript = [
            '// PlayCanvas MMOOMM Multi-Scene - Generated by Universo Platformo',
            `// Total scenes: ${scenes.length}`,
            '',
            spaceScript,
            entityScript,
            componentScript,
            eventScript,
            actionScript,
            dataScript,
            universoScript,
            '',
            'app.start();'
        ].join('\n')

        // Use modular systems manager to generate complete HTML
        return this.builderSystemsManager.generateCompleteHTML(combinedScript, options)
    }

    /**
     * Detect multiplayer mode based on UPDL flow structure
     * Multiplayer mode: Space with collectLeadName=true + connected only to another Space
     */
    private detectMultiplayerMode(flowData: IFlowData): {
        isMultiplayer: boolean
        authSpace?: any
        gameSpace?: any
    } {
        // Only check multi-scene flows for multiplayer pattern
        if (!flowData.multiScene || flowData.multiScene.totalScenes < 2) {
            return { isMultiplayer: false }
        }

        const firstScene = flowData.multiScene.scenes[0]
        const secondScene = flowData.multiScene.scenes[1]

        if (!firstScene || !secondScene) {
            return { isMultiplayer: false }
        }

        // Check if first scene has collectName enabled
        const hasCollectName = firstScene.spaceData?.leadCollection?.collectName === true

        // Check if first scene has minimal game content (auth screen scenario)
        const firstSceneHasMinimalGameContent =
            (!firstScene.spaceData?.entities || firstScene.spaceData.entities.length === 0) ||
            (firstScene.spaceData?.entities && firstScene.spaceData.entities.length <= 2) // Allow minimal entities like basic setup

        // Check if second scene has substantial game content
        const hasGameContent =
            secondScene.dataNodes.length > 0 ||
            secondScene.objectNodes.length > 0 ||
            (secondScene.spaceData?.entities && secondScene.spaceData.entities.length > 0)

        const isMultiplayer = hasCollectName && firstSceneHasMinimalGameContent && hasGameContent

        console.log('[PlayCanvasMMOOMMBuilder] Multiplayer detection:', {
            hasCollectName,
            firstSceneHasMinimalGameContent,
            hasGameContent,
            isMultiplayer,
            firstSceneDataNodes: firstScene.dataNodes.length,
            firstSceneObjectNodes: firstScene.objectNodes.length,
            firstSceneEntities: firstScene.spaceData?.entities?.length || 0,
            secondSceneDataNodes: secondScene.dataNodes.length,
            secondSceneObjectNodes: secondScene.objectNodes.length,
            secondSceneEntities: secondScene.spaceData?.entities?.length || 0
        })

        return {
            isMultiplayer,
            authSpace: isMultiplayer ? firstScene : undefined,
            gameSpace: isMultiplayer ? secondScene : undefined
        }
    }

    /**
     * Build multiplayer scene with auth screen + game
     */
    private buildMultiplayerScene(multiplayerInfo: any, options: BuildOptions): string {
        console.log('[PlayCanvasMMOOMMBuilder] Building multiplayer scene with auth + game')

        // Generate game scene using second scene data
        const gameScene = this.generateGameScene(multiplayerInfo.gameSpace, options)

        // Generate Colyseus client integration
        const colyseusIntegration = this.generateColyseusClient()

        // Combine JavaScript parts only
        const combinedScript = [
            '// PlayCanvas MMOOMM Multiplayer - Generated by Universo Platformo',
            '// Multi-user virtual world with real-time synchronization',
            '',
            gameScene,
            '',
            colyseusIntegration,
            '',
            '// Start PlayCanvas application',
            'app.start();'
        ].join('\n')

        // Generate auth screen HTML separately
        const authScreenHTML = this.generateAuthScreen(multiplayerInfo.authSpace)

        // Use modular systems manager to generate complete HTML with auth screen
        return this.builderSystemsManager.generateCompleteHTML(combinedScript, {
            ...options,
            isMultiplayer: true,
            additionalHTML: authScreenHTML
        })
    }

    /**
     * Extract MMOOMM-specific nodes from flow data
     */
    private extractMMOOMMNodes(flowData: IFlowData): {
        spaces: any[]
        entities: any[]
        components: any[]
        events: any[]
        actions: any[]
        data: any[]
        universo: any[]
    } {
        // Extract base nodes using parent method
        const baseNodes = this.extractNodes(flowData)

        const firstSpace = baseNodes.spaces[0] || {}

        return {
            spaces: baseNodes.spaces,
            entities: (firstSpace as any).entities || [],
            components: (firstSpace as any).components || [],
            events: (firstSpace as any).events || [],
            actions: (firstSpace as any).actions || [],
            data: baseNodes.data,
            universo: (firstSpace as any).universo || []
        }
    }

    /**
     * Generate authentication screen for multiplayer mode
     */
    private generateAuthScreen(authSpace: any): string {
        return `
        <!-- Colyseus Client Integration -->
        <script src="https://unpkg.com/colyseus.js@0.16.4/dist/colyseus.js"></script>

        <!-- Multiplayer Authentication Screen -->
        <div id="multiplayer-auth-screen" style="
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: Arial, sans-serif;
        ">
            <div style="
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 15px;
                text-align: center;
                color: white;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            ">
                <h2 style="margin: 0 0 20px 0; font-size: 24px;">
                    Добро пожаловать в Universo MMOOMM
                </h2>
                <p style="margin: 0 0 30px 0; font-size: 16px; opacity: 0.9;">
                    Введите ваше имя для входа в многопользовательскую игру:
                </p>
                <input
                    type="text"
                    id="player-name-input"
                    placeholder="Ваше имя"
                    style="
                        width: 100%;
                        padding: 15px;
                        margin: 20px 0;
                        border: none;
                        border-radius: 8px;
                        font-size: 16px;
                        box-sizing: border-box;
                        background: rgba(255,255,255,0.9);
                        color: #333;
                    "
                    maxlength="20"
                    required
                >
                <button
                    id="join-game-btn"
                    style="
                        width: 100%;
                        padding: 15px;
                        background: #4CAF50;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 16px;
                        cursor: pointer;
                        transition: background-color 0.3s;
                    "
                    onmouseover="this.style.backgroundColor='#45a049'"
                    onmouseout="this.style.backgroundColor='#4CAF50'"
                >
                    Войти в игру
                </button>
                <div id="connection-status" style="
                    margin-top: 15px;
                    font-size: 14px;
                    opacity: 0.8;
                    display: none;
                ">
                    Подключение к серверу...
                </div>
            </div>
        </div>

        <script>
        // Authentication logic for multiplayer mode
        let playerName = '';

        document.getElementById('join-game-btn').addEventListener('click', function() {
            const nameInput = document.getElementById('player-name-input');
            const name = nameInput.value.trim();

            if (name.length < 2) {
                alert('Имя должно содержать минимум 2 символа');
                return;
            }

            if (name.length > 20) {
                alert('Имя не должно превышать 20 символов');
                return;
            }

            playerName = name;

            // Show connection status
            document.getElementById('connection-status').style.display = 'block';
            document.getElementById('join-game-btn').disabled = true;
            document.getElementById('join-game-btn').textContent = 'Подключение...';

            // Connect to multiplayer server
            connectToMultiplayerServer(playerName);
        });

        // Enter key support
        document.getElementById('player-name-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('join-game-btn').click();
            }
        });

        // Focus on name input when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('player-name-input').focus();
        });
        </script>
        `
    }

    /**
     * Generate game scene from second Space data
     */
    private generateGameScene(gameSpace: any, options: BuildOptions): string {
        // Create a temporary flow data structure for the game scene
        const gameFlowData = {
            updlSpace: gameSpace.spaceData,
            multiScene: undefined
        }

        // Extract nodes from game scene
        const nodes = this.extractMMOOMMNodes(gameFlowData)

        // Process all node types using existing handlers
        const spaceScript = this.spaceHandler.process(nodes.spaces[0], options)
        const entityScript = this.entityHandler.process(nodes.entities, options)
        const componentScript = this.componentHandler.process(nodes.components, options)
        const eventScript = this.eventHandler.process(nodes.events, options)
        const actionScript = this.actionHandler.process(nodes.actions, options)
        const dataScript = this.dataHandler.process(nodes.data, options)

        // Skip UniversoHandler in multiplayer mode (Colyseus handles networking)
        const universoScript = '// Colyseus handles networking in multiplayer mode'

        // Prepare entities data for Colyseus server
        const entitiesForServer = this.prepareEntitiesForColyseus(nodes.entities)

        return [
            '// Game Scene - Generated from second Space',
            `// Entities data for Colyseus server`,
            `window.flowEntitiesData = ${JSON.stringify(entitiesForServer)};`,
            '',
            spaceScript,
            entityScript,
            componentScript,
            eventScript,
            actionScript,
            dataScript,
            universoScript
        ].join('\n')
    }

    /**
     * Prepare entities data for Colyseus server
     */
    private prepareEntitiesForColyseus(entities: any[]): any[] {
        return entities.map(entity => {
            const transform = this.parseTransform(entity.data?.transform)
            const components = entity.data?.components || []

            // Find render component for visual properties
            const renderComponent = components.find((c: any) => c.data?.componentType === 'render')

            return {
                id: entity.id,
                entityType: entity.data?.entityType || 'asteroid',
                position: transform.position,
                rotation: transform.rotation,
                scale: transform.scale,
                tags: entity.data?.tags || '',
                components: components.map((c: any) => ({
                    type: c.data?.componentType,
                    data: c.data
                })),
                visual: renderComponent ? {
                    primitive: renderComponent.data?.primitive || 'box',
                    color: renderComponent.data?.color || '#888888'
                } : null
            }
        })
    }

    /**
     * Parse transform data from various formats
     */
    private parseTransform(transform: any): { position: any, rotation: any, scale: any } {
        if (!transform) {
            return {
                position: { x: 0, y: 0, z: 0 },
                rotation: { x: 0, y: 0, z: 0 },
                scale: { x: 1, y: 1, z: 1 }
            }
        }

        // Handle JSON string format
        if (typeof transform === 'string') {
            try {
                transform = JSON.parse(transform)
            } catch (e) {
                console.warn('[PlayCanvasMMOOMMBuilder] Failed to parse transform:', transform)
                return {
                    position: { x: 0, y: 0, z: 0 },
                    rotation: { x: 0, y: 0, z: 0 },
                    scale: { x: 1, y: 1, z: 1 }
                }
            }
        }

        // Handle array format: {"pos":[0,2,0],"rot":[0,0,0],"scale":[4,4,12]}
        const position = transform.pos ?
            { x: transform.pos[0] || 0, y: transform.pos[1] || 0, z: transform.pos[2] || 0 } :
            transform.position || { x: 0, y: 0, z: 0 }

        const rotation = transform.rot ?
            { x: transform.rot[0] || 0, y: transform.rot[1] || 0, z: transform.rot[2] || 0 } :
            transform.rotation || { x: 0, y: 0, z: 0 }

        const scale = transform.scale ?
            (Array.isArray(transform.scale) ?
                { x: transform.scale[0] || 1, y: transform.scale[1] || 1, z: transform.scale[2] || 1 } :
                transform.scale) :
            { x: 1, y: 1, z: 1 }

        return { position, rotation, scale }
    }

    /**
     * Generate Colyseus client integration (JavaScript only)
     */
    private generateColyseusClient(): string {
        return `
        // Colyseus client for multiplayer mode
        let colyseusClient = null;
        let room = null;
        let otherPlayers = new Map();
        let isConnected = false;

        async function connectToMultiplayerServer(playerName) {
            try {
                console.log('[Multiplayer] Connecting to server with name:', playerName);

                // Initialize SpaceHUD BEFORE connecting to prevent callback errors
                initializeSpaceHUD();

                // Try to connect to local Colyseus server
                colyseusClient = new Colyseus.Client('ws://localhost:2567');

                // Prepare room options with entities data from Flow
                const roomOptions = {
                    name: playerName,
                    entities: window.flowEntitiesData || []
                };

                room = await colyseusClient.joinOrCreate('mmoomm', roomOptions);

                console.log('[Multiplayer] Connected to room:', room.id);
                isConnected = true;

                // Setup event handlers
                // Use Schema v3 API for Colyseus 0.16.x compatibility
                const $ = Colyseus.getStateCallbacks(room);

                // Players join/leave
                $(room.state).players.onAdd((player, sessionId) => {
                    if (sessionId === room.sessionId) {
                        updatePlayerHUD(player);
                    } else {
                        updateOtherPlayer(sessionId, player);
                    }
                });
                $(room.state).players.onRemove((player, sessionId) => {
                    if (otherPlayers && otherPlayers.has(sessionId)) {
                        const ent = otherPlayers.get(sessionId);
                        ent.destroy();
                        otherPlayers.delete(sessionId);
                    }
                });

                // Entities (asteroids)
                if (room.state.asteroids) {
                    $(room.state).asteroids.onAdd((asteroid, id) => {
                        updateAsteroid(id, asteroid);
                    });
                    $(room.state).asteroids.onChange((asteroid, id) => {
                        updateAsteroid(id, asteroid);
                    });
                    $(room.state).asteroids.onRemove((asteroid, id) => {
                        if (window.MMOEntities && window.MMOEntities.has(id)) {
                            const entity = window.MMOEntities.get(id);
                            entity.destroy();
                            window.MMOEntities.delete(id);
                        }
                    });
                }

                room.onMessage('*', onRoomMessage);
                room.onLeave(() => {
                    console.log('[Multiplayer] Left room');
                    isConnected = false;
                });
                room.onError((code, message) => {
                    console.error('[Multiplayer] Error:', code, message);
                    showConnectionError('Ошибка подключения: ' + message);
                });

                // Hide auth screen and start game
                hideAuthScreen();
                startMultiplayerGame();

            } catch (error) {
                console.error('[Multiplayer] Connection failed:', error);
                showConnectionError('Не удалось подключиться к серверу. Убедитесь, что сервер запущен на localhost:2567');
            }
        }

        function hideAuthScreen() {
            document.getElementById('multiplayer-auth-screen').style.display = 'none';
        }

        function showConnectionError(message) {
            document.getElementById('connection-status').textContent = message;
            document.getElementById('connection-status').style.color = '#ff6b6b';
            document.getElementById('join-game-btn').disabled = false;
            document.getElementById('join-game-btn').textContent = 'Попробовать снова';
        }

        function onRoomStateChange(state) {
            if (!state) return;

            // Sync players
            if (state.players) {
                state.players.forEach((player, sessionId) => {
                    if (sessionId === room.sessionId) {
                        // This is our player - update HUD
                        updatePlayerHUD(player);
                    } else {
                        // Other players - update their positions
                        updateOtherPlayer(sessionId, player);
                    }
                });

                // Remove disconnected players
                otherPlayers.forEach((playerEntity, sessionId) => {
                    if (!state.players.has(sessionId)) {
                        playerEntity.destroy();
                        otherPlayers.delete(sessionId);
                        console.log('[Multiplayer] Removed disconnected player:', sessionId);
                    }
                });
            }

            // Sync asteroids
            if (state.asteroids && window.MMOEntities) {
                state.asteroids.forEach((asteroid, id) => {
                    updateAsteroid(id, asteroid);
                });
            }

            // Sync stations
            if (state.stations && window.MMOEntities) {
                state.stations.forEach((station, id) => {
                    updateStation(id, station);
                });
            }
        }

        function onRoomMessage(type, message) {
            console.log('[Multiplayer] Received message:', type, message);
        }

        function updateOtherPlayer(sessionId, playerData) {
            if (!otherPlayers.has(sessionId)) {
                // Create new player entity
                createOtherPlayerEntity(sessionId, playerData);
            } else {
                // Update existing player position
                const playerEntity = otherPlayers.get(sessionId);
                if (playerEntity && playerData) {
                    playerEntity.setPosition(playerData.x, playerData.y, playerData.z);
                    playerEntity.setEulerAngles(playerData.rx, playerData.ry, playerData.rz);
                }
            }
        }

        function createOtherPlayerEntity(sessionId, playerData) {
            if (!window.app) return;

            // Create visual representation of other player
            const otherPlayerEntity = new pc.Entity('OtherPlayer_' + sessionId);

            // Add model component (simple box for now)
            otherPlayerEntity.addComponent('model', { type: 'box' });

            // Create material to distinguish from own ship
            const otherPlayerMaterial = new pc.StandardMaterial();
            otherPlayerMaterial.diffuse.set(0.8, 0.2, 0.2); // Red for other players
            otherPlayerMaterial.update();
            otherPlayerEntity.model.material = otherPlayerMaterial;

            // Set position and scale
            otherPlayerEntity.setPosition(playerData.x, playerData.y, playerData.z);
            otherPlayerEntity.setLocalScale(2, 1, 3); // Ship-like proportions

            // Create name badge
            createPlayerNameBadge(otherPlayerEntity, playerData.name);

            window.app.root.addChild(otherPlayerEntity);
            otherPlayers.set(sessionId, otherPlayerEntity);

            console.log('[Multiplayer] Created other player:', playerData.name);
        }

        function createPlayerNameBadge(playerEntity, playerName) {
            // Create 2D element with player name (billboard effect)
            const nameElement = new pc.Entity('PlayerName');
            nameElement.addComponent('element', {
                type: pc.ELEMENTTYPE_TEXT,
                text: playerName,
                fontSize: 24,
                color: new pc.Color(1, 1, 1),
                anchor: [0.5, 0.5, 0.5, 0.5],
                pivot: [0.5, 0.5]
            });

            // Position above ship
            nameElement.setLocalPosition(0, 3, 0);
            playerEntity.addChild(nameElement);
        }

        // Send player transform updates to server
        function sendPlayerTransform() {
            if (room && window.playerShip && isConnected) {
                const pos = window.playerShip.getPosition();
                const rot = window.playerShip.getEulerAngles();

                room.send('updateTransform', {
                    x: pos.x, y: pos.y, z: pos.z,
                    rx: rot.x, ry: rot.y, rz: rot.z
                });
            }
        }

        // Send mining command to server
        function sendMiningCommand() {
            if (room && isConnected) {
                room.send('startMining', {});
            }
        }

        // Send sell command to server
        function sendSellCommand() {
            if (room && isConnected) {
                room.send('sellAll', {});
            }
        }

        // Start multiplayer game integration
        function startMultiplayerGame() {
            console.log('[Multiplayer] Starting game in multiplayer mode');

            // Start periodic position updates
            setInterval(sendPlayerTransform, 100); // 10 times per second

            // Override mining system to use server authority
            if (window.playerShip && window.playerShip.laserSystem) {
                const originalStartMining = window.playerShip.laserSystem.startMining;
                window.playerShip.laserSystem.startMining = function() {
                    sendMiningCommand();
                    // Don't call original function - server is authoritative
                };
            }

            // Override selling system
            if (window.tradeAll) {
                const originalTradeAll = window.tradeAll;
                window.tradeAll = function() {
                    sendSellCommand();
                    // Don't call original function - server is authoritative
                };
            }

            console.log('[Multiplayer] Multiplayer integration complete');
        }

        function initializeSpaceHUD() {
            // Initialize SpaceHUD for multiplayer mode
            if (!window.SpaceHUD) {
                console.log('[Multiplayer] Initializing SpaceHUD for multiplayer mode');
                window.SpaceHUD = {
                    updateInventory: function(inventory) {
                        console.log('[Multiplayer] HUD: Inventory updated:', inventory);
                        updateInventoryDisplay(inventory);
                    },
                    updateCredits: function(credits) {
                        console.log('[Multiplayer] HUD: Credits updated:', credits);
                        updateCreditsDisplay(credits);
                    }
                };
            }
        }

        function updatePlayerHUD(playerData) {
            // Update HUD with server data
            if (playerData && window.SpaceHUD) {
                if (typeof playerData.inventory !== 'undefined') {
                    window.SpaceHUD.updateInventory(playerData.inventory);
                }
                if (typeof playerData.credits !== 'undefined') {
                    window.SpaceHUD.updateCredits(playerData.credits);
                }
            }
        }

        function updateInventoryDisplay(inventory) {
            // Simple inventory display for multiplayer mode
            let hudElement = document.getElementById('multiplayer-inventory-hud');
            if (!hudElement) {
                hudElement = document.createElement('div');
                hudElement.id = 'multiplayer-inventory-hud';
                hudElement.style.cssText =
                    'position: fixed;' +
                    'top: 20px;' +
                    'left: 20px;' +
                    'background: rgba(0,0,0,0.7);' +
                    'color: white;' +
                    'padding: 10px;' +
                    'border-radius: 5px;' +
                    'font-family: Arial, sans-serif;' +
                    'font-size: 14px;' +
                    'z-index: 1000;';
                document.body.appendChild(hudElement);
            }
            hudElement.innerHTML = 'Инвентарь: ' + (inventory || 0);
        }

        function updateCreditsDisplay(credits) {
            // Simple credits display for multiplayer mode
            let hudElement = document.getElementById('multiplayer-credits-hud');
            if (!hudElement) {
                hudElement = document.createElement('div');
                hudElement.id = 'multiplayer-credits-hud';
                hudElement.style.cssText =
                    'position: fixed;' +
                    'top: 20px;' +
                    'right: 20px;' +
                    'background: rgba(0,0,0,0.7);' +
                    'color: white;' +
                    'padding: 10px;' +
                    'border-radius: 5px;' +
                    'font-family: Arial, sans-serif;' +
                    'font-size: 14px;' +
                    'z-index: 1000;';
                document.body.appendChild(hudElement);
            }
            hudElement.innerHTML = 'Кредиты: ' + (credits || 0);
        }

        function updateAsteroid(asteroidId, asteroidData) {
            // Create or update asteroid from server data
            if (!window.MMOEntities) {
                window.MMOEntities = new Map();
            }

            let asteroid = window.MMOEntities.get(asteroidId);

            if (!asteroid) {
                // Create new asteroid entity
                asteroid = new pc.Entity(asteroidId);

                // Add model component with primitive from server
                const primitive = asteroidData.primitive || 'box';
                asteroid.addComponent('model', { type: primitive });

                // Create material with color from server
                const material = new pc.StandardMaterial();
                const color = asteroidData.color || '#888888';
                const hexColor = color.replace('#', '');
                const r = parseInt(hexColor.substr(0, 2), 16) / 255;
                const g = parseInt(hexColor.substr(2, 2), 16) / 255;
                const b = parseInt(hexColor.substr(4, 2), 16) / 255;
                material.diffuse.set(r, g, b);
                material.update();
                asteroid.model.material = material;

                // Set position and scale from server
                asteroid.setPosition(asteroidData.x, asteroidData.y, asteroidData.z);
                asteroid.setLocalScale(asteroidData.scaleX || 1, asteroidData.scaleY || 1, asteroidData.scaleZ || 1);

                // Add mineable component for resource tracking
                asteroid.mineable = {
                    resourceLeft: asteroidData.resourceLeft || 100,
                    material: asteroidData.material || 'asteroidMass'
                };

                // Add tags for identification
                asteroid.tags = new Set(['asteroid', asteroidData.material || 'asteroidMass']);

                window.app.root.addChild(asteroid);
                window.MMOEntities.set(asteroidId, asteroid);

                console.log('[Multiplayer] Created asteroid:', asteroidId, 'with primitive:', primitive, 'color:', color);
            } else {
                // Update existing asteroid
                if (asteroid.mineable && typeof asteroidData.resourceLeft !== 'undefined') {
                    asteroid.mineable.resourceLeft = asteroidData.resourceLeft;
                }

                // Update position if changed
                if (typeof asteroidData.x !== 'undefined') {
                    asteroid.setPosition(asteroidData.x, asteroidData.y, asteroidData.z);
                }
            }
        }

        function updateStation(stationId, stationData) {
            // Create or update station from server data
            if (!window.MMOEntities) {
                window.MMOEntities = new Map();
            }

            let station = window.MMOEntities.get(stationId);

            if (!station) {
                // Create new station entity
                station = new pc.Entity(stationId);

                // Add model component with primitive from server
                const primitive = stationData.primitive || 'box';
                station.addComponent('model', { type: primitive });

                // Create material with color from server
                const material = new pc.StandardMaterial();
                const color = stationData.color || '#0088ff';
                const hexColor = color.replace('#', '');
                const r = parseInt(hexColor.substr(0, 2), 16) / 255;
                const g = parseInt(hexColor.substr(2, 2), 16) / 255;
                const b = parseInt(hexColor.substr(4, 2), 16) / 255;
                material.diffuse.set(r, g, b);
                material.update();
                station.model.material = material;

                // Set position and scale from server
                station.setPosition(stationData.x, stationData.y, stationData.z);
                station.setLocalScale(stationData.scaleX || 1, stationData.scaleY || 1, stationData.scaleZ || 1);

                // Add trading component
                station.trading = {
                    buyPrice: stationData.buyPrice || 10
                };

                // Add tags for identification
                station.tags = new Set(['station', 'trading']);

                window.app.root.addChild(station);
                window.MMOEntities.set(stationId, station);

                console.log('[Multiplayer] Created station:', stationId, 'with primitive:', primitive, 'color:', color);
            } else {
                // Update existing station
                if (station.trading && typeof stationData.buyPrice !== 'undefined') {
                    station.trading.buyPrice = stationData.buyPrice;
                }

                // Update position if changed
                if (typeof stationData.x !== 'undefined') {
                    station.setPosition(stationData.x, stationData.y, stationData.z);
                }
            }
        }
        `
    }

    /**
     * Generate default scene with configurable demo mode (for empty flows)
     */
    private generateDefaultScene(options: BuildOptions): string {
        return this.builderSystemsManager.generateErrorScene(options)
    }

    /**
     * Implementation of abstract method from AbstractTemplateBuilder
     */
    protected generateHTML(
        content: {
            spaceContent: string
            objectContent: string
            cameraContent: string
            template: string
            error?: boolean
        },
        options: BuildOptions = {}
    ): string {
        // This method is required by AbstractTemplateBuilder but not used in our implementation
        // We use build() method instead for more advanced processing
        return this.builderSystemsManager.generateErrorScene(options)
    }

    /**
     * Get template configuration
     */
    getTemplateInfo(): TemplateConfig {
        return MMOOMMTemplateConfig
    }

    /**
     * Get required libraries for this template
     */
    getRequiredLibraries(): string[] {
        return ['playcanvas']
    }
}
